#=== DATABASE ===
quarkus.datasource.metrics.enabled=true
quarkus.datasource.db-kind=postgresql
quarkus.datasource.jdbc.driver=org.postgresql.Driver
quarkus.datasource.jdbc.min-size=0
quarkus.datasource.jdbc.max-size=50
quarkus.datasource.jdbc.enable-metrics=true
quarkus.hibernate-orm.packages=id.labstech.ai.vision.tablecounter.model
quarkus.hibernate-orm.database.generation=update
quarkus.hibernate-orm.database.default-schema=table_counter_schema
quarkus.datasource.jdbc.url=****************************************************************************************

#=== KAFKA CONFIG ===
namespace=outlet

#=== HTTP CONFIG ===
# quarkus.http.root-path=/
quarkus.http.cors=true
quarkus.http.cors.origins=*
quarkus.http.cors.headers=accept,authorization,content-type,x-requested-with
quarkus.http.cors.methods=GET,POST,PUT,DELETE,OPTIONS
