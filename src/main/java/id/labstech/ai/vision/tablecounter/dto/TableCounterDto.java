package id.labstech.ai.vision.tablecounter.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class TableCounterDto {
    
    @JsonProperty("camera_id")
    private String cameraId;
    
    @JsonProperty("fetcher_id")
    private String fetcherId;
    
    @JsonProperty("frame")
    private String frame;
    
    @JsonProperty("timestamp")
    private Double timestamp;
    
    @JsonProperty("frame_number")
    private Integer frameNumber;
    
    @JsonProperty("path")
    private Object path;
    
    @JsonProperty("metadata")
    private Object metadata;
    
    @JsonProperty("person_tracking")
    private Object personTracking;
    
    @JsonProperty("location_id")
    private String locationId;
    
    @JsonProperty("turnover_data")
    private Object turnoverData;

    public String getCameraId() {
        return cameraId;
    }

    public void setCameraId(String cameraId) {
        this.cameraId = cameraId;
    }

    public String getFetcherId() {
        return fetcherId;
    }

    public void setFetcherId(String fetcherId) {
        this.fetcherId = fetcherId;
    }

    public String getFrame() {
        return frame;
    }

    public void setFrame(String frame) {
        this.frame = frame;
    }

    public Double getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Double timestamp) {
        this.timestamp = timestamp;
    }

    public Integer getFrameNumber() {
        return frameNumber;
    }

    public void setFrameNumber(Integer frameNumber) {
        this.frameNumber = frameNumber;
    }

    public Object getPath() {
        return path;
    }

    public void setPath(Object path) {
        this.path = path;
    }

    public Object getMetadata() {
        return metadata;
    }

    public void setMetadata(Object metadata) {
        this.metadata = metadata;
    }

    public Object getPersonTracking() {
        return personTracking;
    }

    public void setPersonTracking(Object personTracking) {
        this.personTracking = personTracking;
    }

    public String getLocationId() {
        return locationId;
    }

    public void setLocationId(String locationId) {
        this.locationId = locationId;
    }

    public Object getTurnoverData() {
        return turnoverData;
    }

    public void setTurnoverData(Object turnoverData) {
        this.turnoverData = turnoverData;
    }
}