package id.labstech.ai.vision.tablecounter.model;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "table_counter")
public class TableCounter extends PanacheEntityBase {

    @Id
    @GeneratedValue(generator = "UUID")
    @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "id", updatable = false, nullable = false)
    private String id;
    
    @Column(name = "camera_id")
    private String cameraId;
    
    @Column(name = "fetcher_id")
    private String fetcherId;
    
    @Column(name = "frame", columnDefinition = "TEXT")
    private String frame;
    
    @Column(name = "timestamp")
    private Double timestamp;
    
    @Column(name = "frame_number")
    private Integer frameNumber;
    
    @Column(name = "path", columnDefinition = "jsonb")
    private String path;
    
    @Column(name = "metadata", columnDefinition = "jsonb")
    private String metadata;
    
    @Column(name = "person_tracking", columnDefinition = "jsonb")
    private String personTracking;
    
    @Column(name = "location_id")
    private String locationId;

    @Column(name = "location_code")
    private String locationCode;
    
    @Column(name = "turnover_data", columnDefinition = "jsonb")
    private String turnoverData;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    public TableCounter() {
        // Initialize createdAt with current time
        this.createdAt = LocalDateTime.now();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCameraId() {
        return cameraId;
    }

    public void setCameraId(String cameraId) {
        this.cameraId = cameraId;
    }

    public String getFetcherId() {
        return fetcherId;
    }

    public void setFetcherId(String fetcherId) {
        this.fetcherId = fetcherId;
    }

    public String getFrame() {
        return frame;
    }

    public void setFrame(String frame) {
        this.frame = frame;
    }

    public Double getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Double timestamp) {
        this.timestamp = timestamp;
    }

    public Integer getFrameNumber() {
        return frameNumber;
    }

    public void setFrameNumber(Integer frameNumber) {
        this.frameNumber = frameNumber;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getMetadata() {
        return metadata;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public String getPersonTracking() {
        return personTracking;
    }

    public void setPersonTracking(String personTracking) {
        this.personTracking = personTracking;
    }

    public String getLocationId() {
        return locationId;
    }

    public void setLocationId(String locationId) {
        this.locationId = locationId;
    }

    public String getTurnoverData() {
        return turnoverData;
    }

    public void setTurnoverData(String turnoverData) {
        this.turnoverData = turnoverData;
    }

    public String getLocationCode() {
        return locationCode;
    }

    public void setLocationCode(String locationCode) {
        this.locationCode = locationCode;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    // Convert LocalDateTime to Instant for API compatibility
    public Instant getCreatedAtInstant() {
        return getCreatedAt().atZone(ZoneId.systemDefault()).toInstant();
    }

    // Set createdAt using Instant
    public void setCreatedAtInstant(Instant instant) {
        setCreatedAt(LocalDateTime.ofInstant(instant, ZoneId.systemDefault()));
    }
}