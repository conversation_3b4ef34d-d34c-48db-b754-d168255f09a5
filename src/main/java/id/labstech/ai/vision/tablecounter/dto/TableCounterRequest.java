package id.labstech.ai.vision.tablecounter.dto;

public class TableCounterRequest {
    private String type;
    private TimeframeRequest data;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public TimeframeRequest getData() {
        return data;
    }

    public void setData(TimeframeRequest data) {
        this.data = data;
    }

    public static class TimeframeRequest {
        private String startTime;           // Format: "yyyy-MM-dd HH:mm:ss"
        private String endTime;             // Optional: Format: "yyyy-MM-dd HH:mm:ss"
        private Integer updateFrequency;    // Update frequency in seconds (default: 1)
        private String cameraId;            // Optional: Filter by camera ID
        private String locationCode;        // Optional: Filter by location code

        public String getStartTime() {
            return startTime;
        }

        public java.time.Instant getStartTimeAsInstant() {
            if (startTime == null) {
                return null;
            }
            return java.time.LocalDateTime.parse(startTime, DATE_FORMATTER)
                    .atZone(java.time.ZoneId.systemDefault())
                    .toInstant();
        }

        public static final java.time.format.DateTimeFormatter DATE_FORMATTER = java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        public void setStartTime(String startTime) {
            if (startTime != null) {
                try {
                    java.time.LocalDateTime.parse(startTime, DATE_FORMATTER);
                } catch (java.time.format.DateTimeParseException e) {
                    throw new id.labstech.ai.vision.tablecounter.exception.ValidationException("Invalid startTime format. Expected format: yyyy-MM-dd HH:mm:ss");
                }
            }
            this.startTime = startTime;
        }

        public String getEndTime() {
            return endTime;
        }

        public void setEndTime(String endTime) {
            if (endTime != null) {
                try {
                    java.time.LocalDateTime.parse(endTime, DATE_FORMATTER);
                } catch (java.time.format.DateTimeParseException e) {
                    throw new id.labstech.ai.vision.tablecounter.exception.ValidationException("Invalid endTime format. Expected format: yyyy-MM-dd HH:mm:ss");
                }
            }
            this.endTime = endTime;
        }

        public Integer getUpdateFrequency() {
            return updateFrequency != null ? updateFrequency : 1;
        }

        public void setUpdateFrequency(Integer updateFrequency) {
            this.updateFrequency = updateFrequency;
        }

        public String getCameraId() {
            return cameraId;
        }

        public void setCameraId(String cameraId) {
            this.cameraId = cameraId;
        }

        public String getLocationCode() {
            return locationCode;
        }

        public void setLocationCode(String locationCode) {
            this.locationCode = locationCode;
        }
    }
}