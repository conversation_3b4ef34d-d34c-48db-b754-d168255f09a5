package id.labstech.ai.vision.tablecounter.websocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import id.labstech.ai.vision.tablecounter.dto.TableCounterRequest;
import id.labstech.ai.vision.tablecounter.dto.TableCounterResponse;
import id.labstech.ai.vision.tablecounter.model.TableCounter;
import id.labstech.ai.vision.tablecounter.service.TableCounterService;
import io.quarkus.runtime.ExecutorRecorder;
import io.quarkus.scheduler.Scheduled;
import io.smallrye.mutiny.Uni;
import io.vertx.mutiny.core.Vertx;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;


@ServerEndpoint("/table-counter/ws")
@ApplicationScoped
public class TableCounterWebSocket {

    @Inject
    TableCounterService tableCounterService;

    @Inject
    ObjectMapper objectMapper;

    private final Map<String, Session> sessions = new ConcurrentHashMap<>();
    private final Map<String, TableCounterRequest.TimeframeRequest> sessionConfigs = new ConcurrentHashMap<>();
    private final Map<String, Instant> lastUpdateTimes = new ConcurrentHashMap<>();
    private final Map<String, Boolean> streamingModes = new ConcurrentHashMap<>();
    private final Map<String, String> sessionCameraIds = new ConcurrentHashMap<>();
    private final Map<String, String> sessionLocationCodes = new ConcurrentHashMap<>();
    
    // Memory optimization constants - More aggressive settings
    private static final int MAX_CHUNK_SIZE = 20; // Further reduced from 50 to 20
    private static final int CHUNK_DELAY_MS = 50; // Increased from 20ms to 50ms
    private static final int MAX_CONCURRENT_PROCESSING = 2; // Reduced from 3 to 2
    private static final int GC_CHUNK_INTERVAL = 5; // More frequent GC - every 5 chunks
    private static final double MEMORY_THRESHOLD = 0.75; // 75% memory usage threshold
    private final AtomicInteger activeProcessingCount = new AtomicInteger(0);

    @OnOpen
    public void onOpen(Session session) {
        sessions.put(session.getId(), session);
    }

    @OnClose
    public void onClose(Session session) {
        cleanupSession(session.getId());
    }

    @OnError
    public void onError(Session session, Throwable throwable) {
        cleanupSession(session.getId());
        sendError(session, "Error: " + throwable.getMessage());
    }

    @Inject
    Vertx vertx;

    @OnMessage
    public void onMessage(String message, Session session) {
        try {
            TableCounterRequest request = objectMapper.readValue(message, TableCounterRequest.class);
            
            if ("SUBSCRIBE".equals(request.getType())) {
                // Store the configuration for this session
                sessionConfigs.put(session.getId(), request.getData());
                lastUpdateTimes.put(session.getId(), Instant.now());
                streamingModes.put(session.getId(), true);
                
                // Store camera ID if provided
                if (request.getData().getCameraId() != null) {
                    sessionCameraIds.put(session.getId(), request.getData().getCameraId());
                }

                // Store location code if provided
                if (request.getData().getLocationCode() != null) {
                    sessionLocationCodes.put(session.getId(), request.getData().getLocationCode());
                }
                
                // Send confirmation immediately
                sendToSession(session, TableCounterResponse.info(
                    String.format("Subscribed successfully. Update frequency: %d seconds", 
                    request.getData().getUpdateFrequency())
                ));

                // Process historical data in worker thread with throttling
                if (activeProcessingCount.get() < MAX_CONCURRENT_PROCESSING) {
                    activeProcessingCount.incrementAndGet();
                    
                    Uni.createFrom().item(() -> {
                        String cameraId = sessionCameraIds.get(session.getId());
                        String locationCode = sessionLocationCodes.get(session.getId());
                        
                        if (cameraId != null) {
                            return tableCounterService.getStreamingDataByCameraId(session.getId(), cameraId, request.getData());
                        } else if (locationCode != null) {
                            return tableCounterService.getStreamingDataByLocationCode(session.getId(), locationCode, request.getData());
                        } else {
                            return tableCounterService.getStreamingData(session.getId(), request.getData());
                        }
                    })
                    .runSubscriptionOn(ExecutorRecorder.getCurrent())
                    .onItem().invoke(data -> {
                        sendDataInOptimizedChunks(session, data);
                    }).subscribe().with(
                        success -> activeProcessingCount.decrementAndGet(), 
                        error -> {
                            activeProcessingCount.decrementAndGet();
                            sendError(session, "Failed to fetch historical data: " + error.getMessage());
                        }
                    );
                } else {
                    sendError(session, "Server is busy, please try again later");
                }
            } else if ("UNSUBSCRIBE".equals(request.getType())) {
                cleanupSession(session.getId());
                sendToSession(session, TableCounterResponse.info("Unsubscribed successfully"));
            }
        } catch (Exception e) {
            sendError(session, "Failed to process message: " + e.getMessage());
        }
    }

    @Scheduled(every="1s")
    void sendUpdates() {
        sessions.forEach((sessionId, session) -> {
            try {
                TableCounterRequest.TimeframeRequest config = sessionConfigs.get(sessionId);
                Boolean isStreaming = streamingModes.get(sessionId);
                
                if (config != null && isStreaming != null && isStreaming) {
                    Instant lastUpdate = lastUpdateTimes.get(sessionId);
                    Instant now = Instant.now();
                    
                    // Check if it's time to send an update based on the configured frequency
                    if (lastUpdate.plusSeconds(config.getUpdateFrequency()).isBefore(now)) {
                        // Process updates in worker thread with throttling
                        if (activeProcessingCount.get() < MAX_CONCURRENT_PROCESSING) {
                            activeProcessingCount.incrementAndGet();
                            
                            Uni.createFrom().item(() -> {
                                String cameraId = sessionCameraIds.get(sessionId);
                                String locationCode = sessionLocationCodes.get(sessionId);
                                
                                if (cameraId != null) {
                                    return tableCounterService.getStreamingDataByCameraId(sessionId, cameraId, config);
                                } else if (locationCode != null) {
                                    return tableCounterService.getStreamingDataByLocationCode(sessionId, locationCode, config);
                                } else {
                                    return tableCounterService.getStreamingData(sessionId, config);
                                }
                            })
                            .runSubscriptionOn(ExecutorRecorder.getCurrent())
                            .onItem().invoke(data -> {
                                sendDataInOptimizedChunks(session, data);
                                lastUpdateTimes.put(sessionId, now);
                            }).subscribe().with(
                                success -> activeProcessingCount.decrementAndGet(), 
                                error -> {
                                    activeProcessingCount.decrementAndGet();
                                    sendError(session, "Failed to fetch updates: " + error.getMessage());
                                }
                            );
                        }
                    }
                }
            } catch (Exception e) {
                sendError(session, "Failed to send update: " + e.getMessage());
            }
        });
    }

    /**
     * Check memory usage and return true if memory is critically low
     */
    private boolean isMemoryCritical() {
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        
        double memoryUsage = (double) usedMemory / maxMemory;
        return memoryUsage > MEMORY_THRESHOLD;
    }

    private void sendDataInOptimizedChunks(Session session, List<TableCounter> data) {
        if (data == null || data.isEmpty()) {
            return;
        }
        
        try {
            int chunkCount = 0;
            
            // Process data in smaller chunks with memory-friendly approach
            for (int i = 0; i < data.size(); i += MAX_CHUNK_SIZE) {
                // Check memory before processing each chunk
                if (isMemoryCritical()) {
                    System.gc();
                    Runtime.getRuntime().gc();
                    
                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                    
                    // If still critical after GC, stop sending
                    if (isMemoryCritical()) {
                        sendError(session, "Memory critically low, stopping data transmission");
                        return;
                    }
                }
                
                int end = Math.min(i + MAX_CHUNK_SIZE, data.size());
                List<TableCounter> chunk = data.subList(i, end);
                
                // Send each item in the chunk individually to reduce memory footprint
                for (TableCounter counter : chunk) {
                    if (session.isOpen()) {
                        sendToSession(session, TableCounterResponse.fromTableCounter(counter));
                    } else {
                        return; // Session closed, stop sending
                    }
                }
                
                chunkCount++;
                
                // More aggressive GC every GC_CHUNK_INTERVAL chunks
                if (chunkCount % GC_CHUNK_INTERVAL == 0) {
                    System.gc();
                    Runtime.getRuntime().gc();
                    
                    // Longer delay for GC to complete
                    try {
                        Thread.sleep(25); // Additional delay for GC
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
                
                // Add delay between chunks to prevent overwhelming the WebSocket
                // and allow GC to clean up
                if (i + MAX_CHUNK_SIZE < data.size()) {
                    try {
                        Thread.sleep(CHUNK_DELAY_MS);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
            
            // Final aggressive GC call after sending all data
            if (chunkCount > 0) {
                System.gc();
                Runtime.getRuntime().gc();
                
                // Final cleanup delay
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
            
        } catch (Exception e) {
            sendError(session, "Failed to send data chunks: " + e.getMessage());
        }
    }

    private void sendToSession(Session session, TableCounterResponse response) {
        try {
            if (session.isOpen()) {
                session.getAsyncRemote().sendText(objectMapper.writeValueAsString(response));
            }
        } catch (Exception e) {
            sendError(session, "Failed to send message: " + e.getMessage());
        }
    }

    private void sendError(Session session, String error) {
        try {
            if (session.isOpen()) {
                TableCounterResponse errorResponse = TableCounterResponse.error(error);
                session.getAsyncRemote().sendText(objectMapper.writeValueAsString(errorResponse));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * Clean up all session-related data and call garbage collection
     */
    private void cleanupSession(String sessionId) {
        sessions.remove(sessionId);
        sessionConfigs.remove(sessionId);
        lastUpdateTimes.remove(sessionId);
        streamingModes.remove(sessionId);
        sessionCameraIds.remove(sessionId);
        sessionLocationCodes.remove(sessionId);
        tableCounterService.clearStreamingState(sessionId);
        
        // Aggressive GC after cleanup to free memory immediately
        System.gc();
        Runtime.getRuntime().gc();
        
        // Allow time for cleanup
        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}