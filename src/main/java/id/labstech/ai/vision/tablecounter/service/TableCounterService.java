package id.labstech.ai.vision.tablecounter.service;

import id.labstech.ai.vision.tablecounter.dao.TableCounterDao;
import id.labstech.ai.vision.tablecounter.dto.TableCounterRequest;
import id.labstech.ai.vision.tablecounter.dto.TableCounterRequest.TimeframeRequest;
import id.labstech.ai.vision.tablecounter.model.TableCounter;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.transaction.Transactional;

@ApplicationScoped
public class TableCounterService {

    @Inject
    TableCounterDao tableCounterDao;

    private final ConcurrentHashMap<String, Instant> lastProcessedTimestamps = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Instant> sessionLastAccessTime = new ConcurrentHashMap<>();
    private ScheduledExecutorService cleanupExecutor;
    
    // Session timeout in minutes (clean up inactive sessions)
    private static final long SESSION_TIMEOUT_MINUTES = 30;
    private static final long CLEANUP_INTERVAL_MINUTES = 5;
    
    // Pagination constants - More aggressive memory management
    private static final int PAGE_SIZE = 200; // Reduced from 500 to 200
    private static final int GC_INTERVAL_PAGES = 2; // Call GC every 2 pages (more frequent)
    
    // Memory monitoring constants
    private static final double MEMORY_THRESHOLD = 0.8; // 80% memory usage threshold

    @PostConstruct
    void init() {
        cleanupExecutor = Executors.newSingleThreadScheduledExecutor();
        // Schedule periodic cleanup of inactive sessions
        cleanupExecutor.scheduleAtFixedRate(this::cleanupInactiveSessions, 
            CLEANUP_INTERVAL_MINUTES, CLEANUP_INTERVAL_MINUTES, TimeUnit.MINUTES);
    }

    @PreDestroy
    void destroy() {
        if (cleanupExecutor != null) {
            cleanupExecutor.shutdown();
        }
    }

    private void cleanupInactiveSessions() {
        Instant cutoff = Instant.now().minusSeconds(SESSION_TIMEOUT_MINUTES * 60);
        sessionLastAccessTime.entrySet().removeIf(entry -> {
            if (entry.getValue().isBefore(cutoff)) {
                lastProcessedTimestamps.remove(entry.getKey());
                return true;
            }
            return false;
        });
    }

    @Transactional
    public List<TableCounter> getDataByTimeframe(TimeframeRequest request) {
        Instant startTime = request.getStartTimeAsInstant();
        Instant endTime = request.getEndTime() != null ? 
            java.time.LocalDateTime.parse(request.getEndTime(), TableCounterRequest.TimeframeRequest.DATE_FORMATTER)
                .atZone(java.time.ZoneId.systemDefault())
                .toInstant() 
            : null;
        
        return tableCounterDao.findByTimeRange(startTime, endTime);
    }

    @Transactional
    public List<TableCounter> getLatestData(TimeframeRequest request) {
        return tableCounterDao.findLatestData(
            Instant.now().minusSeconds(request.getUpdateFrequency())
        );
    }

    @Transactional
    public List<TableCounter> getStreamingData(String sessionId, TimeframeRequest request) {
        // Update session access time for cleanup tracking
        sessionLastAccessTime.put(sessionId, Instant.now());
        
        Instant startTime;
        
        if (lastProcessedTimestamps.containsKey(sessionId)) {
            // Get data since the last processed timestamp
            startTime = lastProcessedTimestamps.get(sessionId);
        } else {
            // First request, use the provided start time
            startTime = request.getStartTimeAsInstant();
        }

        // Get ALL data from startTime up to now using pagination
        List<TableCounter> allData = getAllDataWithPagination(
            (offset) -> tableCounterDao.findByTimeRangeWithPagination(startTime, Instant.now(), PAGE_SIZE, offset)
        );
        
        // Update the last processed timestamp if we got any data
        if (!allData.isEmpty()) {
            // Find the latest timestamp in the data
            Instant latestTimestamp = allData.stream()
                .map(counter -> Instant.ofEpochSecond(counter.getTimestamp().longValue()))
                .max(Instant::compareTo)
                .orElse(startTime);
            
            // Store the latest timestamp plus 1 millisecond to avoid duplicates
            lastProcessedTimestamps.put(sessionId, latestTimestamp.plusMillis(1));
        }

        return allData;
    }

    @Transactional
    public List<TableCounter> getStreamingDataByCameraId(String sessionId, String cameraId, TimeframeRequest request) {
        // Update session access time for cleanup tracking
        sessionLastAccessTime.put(sessionId, Instant.now());
        
        Instant startTime = getStartTimeForSession(sessionId, request);
        String locationCode = request.getLocationCode();

        // Get ALL data from startTime up to now using pagination
        List<TableCounter> allData;
        if (locationCode != null) {
            allData = getAllDataWithPagination(
                (offset) -> tableCounterDao.findLatestDataByCameraIdAndLocationCodeWithPagination(cameraId, locationCode, startTime, PAGE_SIZE, offset)
            );
        } else {
            allData = getAllDataWithPagination(
                (offset) -> tableCounterDao.findLatestDataByCameraIdWithPagination(cameraId, startTime, PAGE_SIZE, offset)
            );
        }
        
        updateLastProcessedTimestamp(sessionId, startTime, allData);
        return allData;
    }

    @Transactional
    public List<TableCounter> getStreamingDataByLocationCode(String sessionId, String locationCode, TimeframeRequest request) {
        // Update session access time for cleanup tracking
        sessionLastAccessTime.put(sessionId, Instant.now());
        
        Instant startTime = getStartTimeForSession(sessionId, request);
        
        // Get ALL data from startTime up to now using pagination
        List<TableCounter> allData = getAllDataWithPagination(
            (offset) -> tableCounterDao.findLatestDataByLocationCodeWithPagination(locationCode, startTime, PAGE_SIZE, offset)
        );
        
        updateLastProcessedTimestamp(sessionId, startTime, allData);
        return allData;
    }

    private Instant getStartTimeForSession(String sessionId, TimeframeRequest request) {
        if (lastProcessedTimestamps.containsKey(sessionId)) {
            // Get data since the last processed timestamp
            return lastProcessedTimestamps.get(sessionId);
        } else {
            // First request, use the provided start time
            return request.getStartTimeAsInstant();
        }
    }

    private void updateLastProcessedTimestamp(String sessionId, Instant startTime, List<TableCounter> data) {
        if (!data.isEmpty()) {
            // Find the latest timestamp in the data
            Instant latestTimestamp = data.stream()
                .map(counter -> Instant.ofEpochSecond(counter.getTimestamp().longValue()))
                .max(Instant::compareTo)
                .orElse(startTime);
            
            // Store the latest timestamp plus 1 millisecond to avoid duplicates
            lastProcessedTimestamps.put(sessionId, latestTimestamp.plusMillis(1));
        }
    }

    public void clearStreamingState(String sessionId) {
        lastProcessedTimestamps.remove(sessionId);
        sessionLastAccessTime.remove(sessionId);
    }
    
    /**
     * Check memory usage and return true if memory is critically low
     */
    private boolean isMemoryCritical() {
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        
        double memoryUsage = (double) usedMemory / maxMemory;
        return memoryUsage > MEMORY_THRESHOLD;
    }
    
    /**
     * Get all data using pagination with memory optimization and GC calls
     */
    private List<TableCounter> getAllDataWithPagination(Function<Integer, List<TableCounter>> dataFetcher) {
        List<TableCounter> allData = new ArrayList<>();
        int offset = 0;
        int pageCount = 0;
        
        while (true) {
            // Check memory before fetching more data
            if (isMemoryCritical()) {
                // Force aggressive GC before continuing
                System.gc();
                Runtime.getRuntime().gc();
                
                try {
                    Thread.sleep(200); // Longer wait for critical memory
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
                
                // Check again after GC
                if (isMemoryCritical()) {
                    System.err.println("Memory critically low, stopping pagination at page " + pageCount);
                    break;
                }
            }
            
            // Fetch one page of data
            List<TableCounter> pageData = dataFetcher.apply(offset);
            
            // If no more data, break the loop
            if (pageData == null || pageData.isEmpty()) {
                break;
            }
            
            // Add page data to result
            allData.addAll(pageData);
            
            // If page is not full, we've reached the end
            if (pageData.size() < PAGE_SIZE) {
                break;
            }
            
            // Move to next page
            offset += PAGE_SIZE;
            pageCount++;
            
            // Call System.gc() every GC_INTERVAL_PAGES to help with memory management
            if (pageCount % GC_INTERVAL_PAGES == 0) {
                // More aggressive GC with longer delay
                System.gc();
                
                // Longer delay to allow GC to complete properly
                try {
                    Thread.sleep(50); // Increased from 10ms to 50ms
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
                
                // Additional runtime suggestion for GC
                Runtime.getRuntime().gc();
            }
        }
        
        // Final aggressive GC call after processing all pages
        if (pageCount > 0) {
            System.gc();
            Runtime.getRuntime().gc();
            
            // Final delay to ensure memory cleanup
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        
        return allData;
    }
}