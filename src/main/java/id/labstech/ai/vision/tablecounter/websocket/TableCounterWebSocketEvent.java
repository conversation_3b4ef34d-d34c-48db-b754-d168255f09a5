package id.labstech.ai.vision.tablecounter.websocket;

import com.fasterxml.jackson.databind.JsonNode;

public class TableCounterWebSocketEvent {
    private final String type;
    private final JsonNode data;
    private final String rawMessage;
    private Exception error;
    private final long timestamp;
    
    public TableCounterWebSocketEvent(String type, JsonNode data, String rawMessage) {
        this.type = type;
        this.data = data;
        this.rawMessage = rawMessage;
        this.timestamp = System.currentTimeMillis();
    }
    
    public String getType() { return type; }
    public JsonNode getData() { return data; }
    public String getRawMessage() { return rawMessage; }
    public Exception getError() { return error; }
    public long getTimestamp() { return timestamp; }
    
    public void setError(Exception error) { this.error = error; }
    
    public boolean isError() { return error != null; }
    
    @Override
    public String toString() {
        return String.format("TableCounterWebSocketEvent{type='%s', timestamp=%d, hasError=%s}", 
                           type, timestamp, isError());
    }
}
