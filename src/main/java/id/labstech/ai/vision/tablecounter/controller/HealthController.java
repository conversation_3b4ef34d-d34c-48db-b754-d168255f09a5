package id.labstech.ai.vision.tablecounter.controller;

import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

@Path("/table-counter/health")
public class HealthController {

    @GET
    @Produces(MediaType.APPLICATION_JSON)
    public Response health() {
        return Response.ok()
                .entity("{\"status\":\"UP\",\"service\":\"table-counter-service\"}")
                .build();
    }
}
