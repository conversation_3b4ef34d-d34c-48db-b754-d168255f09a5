package id.labstech.ai.vision.tablecounter.util;

import id.labstech.ai.vision.tablecounter.exception.RareException;
import io.quarkus.runtime.StartupEvent;
import io.vertx.core.json.JsonObject;
import java.io.IOException;
import java.net.URI;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import javax.enterprise.context.ApplicationScoped;
import javax.enterprise.event.Observes;
import javax.net.ssl.SSLContext;
import javax.ws.rs.core.MediaType;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


@ApplicationScoped
public class HttpUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(HttpUtil.class);
    private HttpClientBuilder httpClientBuilder;

    void onStart(@Observes StartupEvent ev) throws NoSuchAlgorithmException, KeyStoreException, KeyManagementException {
        SSLContext sslContext = SSLContexts.custom().loadTrustMaterial(null, new TrustSelfSignedStrategy()).build();
        SSLConnectionSocketFactory sslConSocFactory = new SSLConnectionSocketFactory(sslContext,
                new NoopHostnameVerifier());
        httpClientBuilder = HttpClients.custom().setSSLSocketFactory(sslConSocFactory);
    }

    public static JsonObject httpPostRequest(String path, JsonObject data) throws IOException, RareException {
        URI uri = URI.create(path != null ? path : "");

        try (CloseableHttpClient httpClient = HttpClientBuilder.create().build()) {
            HttpPost httpPost = new HttpPost(uri);
            httpPost.setHeader("X-Consumer-Custom-ID","");
            httpPost.setHeader("Content-Type", "application/json");
            StringEntity requestBody = new StringEntity(data.encode());
            httpPost.setEntity(requestBody);
            try (CloseableHttpResponse httpResponse = httpClient.execute(httpPost)) {
                int statusCode = httpResponse.getStatusLine().getStatusCode();
                HttpEntity entity = httpResponse.getEntity();
                String response = entity != null ? EntityUtils.toString(entity) : "";
                
                if (statusCode == 204) {
                    return new JsonObject();
                }

                if (statusCode != 200) {
                    throw new RareException(response);
                }

                if (response == null || response.isEmpty()) {
                    return new JsonObject();
                }

                return new JsonObject(response);
            }
        }
    }

    public static JsonObject httpPostRequest(String path, JsonObject data, JsonObject header) throws IOException, RareException {
        URI uri = URI.create(path != null ? path : "");

        try (CloseableHttpClient httpClient = HttpClientBuilder.create().build()) {
            HttpPost httpPost = new HttpPost(uri);
            httpPost.setHeader("Content-Type", "application/json");
            for (String key: header.fieldNames()) {
                httpPost.setHeader(key, header.getString(key));
            }
            StringEntity requestBody = new StringEntity(data.encode());
            httpPost.setEntity(requestBody);
            try (CloseableHttpResponse httpResponse = httpClient.execute(httpPost)) {
                HttpEntity entity = httpResponse.getEntity();
                String response = EntityUtils.toString(entity);
                JsonObject jsonResponse = new JsonObject(response);
                if (httpResponse.getStatusLine().getStatusCode() != 200) {
                    throw new RareException(response);
                }
                return jsonResponse;
            }
        }
    }

    public static JsonObject httpGetRequest(String path) throws IOException, RareException {
        URI uri = URI.create(path != null ? path : "");
        try (CloseableHttpClient httpClient = HttpClientBuilder.create().build()) {
            HttpGet httpGet = new HttpGet(uri);
            httpGet.setHeader("Authorization","");
            httpGet.setHeader("Content-Type", MediaType.APPLICATION_JSON);
            try (CloseableHttpResponse httpResponse = httpClient.execute(httpGet)) {
                HttpEntity entity = httpResponse.getEntity();
                String response = EntityUtils.toString(entity);
                JsonObject jsonResponse = new JsonObject(response);
                if (httpResponse.getStatusLine().getStatusCode() != 200) {
                    throw new RareException(response);
                }
                return jsonResponse;
            }
        }
    }

    public static JsonObject httpGetRequestInternal(String path, String customId) throws IOException, RareException {
        URI uri = URI.create(path != null ? path : "");
        try (CloseableHttpClient httpClient = HttpClientBuilder.create().build()) {
            HttpGet httpGet = new HttpGet(uri);
            httpGet.setHeader("X-Consumer-Custom-ID", customId);
            httpGet.setHeader("Content-Type", MediaType.APPLICATION_JSON);
            try (CloseableHttpResponse httpResponse = httpClient.execute(httpGet)) {
                HttpEntity entity = httpResponse.getEntity();
                String response = EntityUtils.toString(entity);
                JsonObject jsonResponse = new JsonObject(response);
                if (httpResponse.getStatusLine().getStatusCode() != 200) {
                    throw new RareException(response);
                }
                return jsonResponse;
            }
        }
    }
}