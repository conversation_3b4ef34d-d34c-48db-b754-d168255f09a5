package id.labstech.ai.vision.tablecounter.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class BasicUtil {
        public static List<Map<String, Object>> createListOfMapFromArray(List<Object[]> list, String ... columnNames) throws Exception{

        if (list == null) {
            return new ArrayList<Map<String, Object>>();
        }

        if (list.size() > 0) {
            if(list.get(0).length > columnNames.length) {
                throw new Exception("Invalid Argument");
            }
        }

        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
        for (Object[] item : list) {
            Map<String, Object> temp = new HashMap<String, Object>();
            for (int i = 0; i < columnNames.length; i++) {
                if(item[i] instanceof Timestamp) {
                    temp.put(columnNames[i], DateUtil.convertFromLocalDateTimeToString(((Timestamp)item[i]).toLocalDateTime()));
                } else {
                    temp.put(columnNames[i], item[i]);
                }
            }
            result.add(temp);
        }

        return result;
    }

    public static String toJson(Object object) {
        String mergedJson = null;
        try {
            mergedJson = new ObjectMapper().registerModule(new JavaTimeModule()).writeValueAsString(object);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        return mergedJson;
    }

    public static boolean isEmpty(Map<?,?> maps){
        return maps == null || maps.isEmpty();
    }

    public static String numberFormat(String value) {
        DecimalFormat formatter = new DecimalFormat("#,##0");
        formatter.setRoundingMode(RoundingMode.HALF_UP);
    
        String result = formatter.format(new BigDecimal(value)).replace(",", ".");
        return result;
    }
}
