package id.labstech.ai.vision.tablecounter.dao;

import id.labstech.ai.vision.tablecounter.model.TableCounter;
import java.time.Instant;
import java.util.List;
import javax.enterprise.context.ApplicationScoped;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.TypedQuery;

@ApplicationScoped
public class TableCounterDao {

    @PersistenceContext
    EntityManager entityManager;
    
    // Default limits to prevent memory issues - More aggressive settings
    private static final int DEFAULT_MAX_RESULTS = 500; // Reduced from 1000
    private static final int STREAMING_PAGE_SIZE = 20; // Reduced from 200 to 20

    public List<TableCounter> findByTimeRange(Instant startTime, Instant endTime) {
        return findByTimeRange(startTime, endTime, DEFAULT_MAX_RESULTS);
    }
    
    public List<TableCounter> findByTimeRange(Instant startTime, Instant endTime, int maxResults) {
        return findByTimeRangeWithPagination(startTime, endTime, maxResults, 0);
    }
    
    public List<TableCounter> findByTimeRangeWithPagination(Instant startTime, Instant endTime, int maxResults, int offset) {
        double startTimestamp = startTime.getEpochSecond() + startTime.getNano() / 1_000_000_000.0;
        Double endTimestamp = endTime != null ? 
            endTime.getEpochSecond() + endTime.getNano() / 1_000_000_000.0 : null;
        
        String queryStr = endTimestamp != null ?
            "SELECT t FROM TableCounter t WHERE t.timestamp >= :startTime AND t.timestamp <= :endTime ORDER BY t.timestamp ASC, t.frameNumber ASC" :
            "SELECT t FROM TableCounter t WHERE t.timestamp >= :startTime ORDER BY t.timestamp ASC, t.frameNumber ASC";
        
        TypedQuery<TableCounter> query = entityManager.createQuery(queryStr, TableCounter.class)
            .setParameter("startTime", startTimestamp)
            .setMaxResults(maxResults)
            .setFirstResult(offset);
            
        if (endTimestamp != null) {
            query.setParameter("endTime", endTimestamp);
        }
            
        return query.getResultList();
    }

    public List<TableCounter> findLatestData(Instant since) {
        return findLatestDataWithPagination(since, STREAMING_PAGE_SIZE, 0);
    }
    
    public List<TableCounter> findLatestDataWithPagination(Instant since, int maxResults, int offset) {
        double sinceTimestamp = since.getEpochSecond() + since.getNano() / 1_000_000_000.0;
        
        String queryStr = "SELECT t FROM TableCounter t WHERE t.timestamp >= :since ORDER BY t.timestamp ASC, t.frameNumber ASC";
        
        return entityManager.createQuery(queryStr, TableCounter.class)
            .setParameter("since", sinceTimestamp)
            .setMaxResults(maxResults)
            .setFirstResult(offset)
            .getResultList();
    }

    public List<TableCounter> findLatestDataByCameraId(String cameraId, Instant since) {
        return findLatestDataByCameraIdWithPagination(cameraId, since, STREAMING_PAGE_SIZE, 0);
    }
    
    public List<TableCounter> findLatestDataByCameraIdWithPagination(String cameraId, Instant since, int maxResults, int offset) {
        double sinceTimestamp = since.getEpochSecond() + since.getNano() / 1_000_000_000.0;
        
        String queryStr = "SELECT t FROM TableCounter t WHERE t.timestamp >= :since AND t.cameraId = :cameraId ORDER BY t.timestamp ASC, t.frameNumber ASC";
        
        return entityManager.createQuery(queryStr, TableCounter.class)
            .setParameter("since", sinceTimestamp)
            .setParameter("cameraId", cameraId)
            .setMaxResults(maxResults)
            .setFirstResult(offset)
            .getResultList();
    }

    public List<TableCounter> findLatestDataByLocationCode(String locationCode, Instant since) {
        return findLatestDataByLocationCodeWithPagination(locationCode, since, STREAMING_PAGE_SIZE, 0);
    }
    
    public List<TableCounter> findLatestDataByLocationCodeWithPagination(String locationCode, Instant since, int maxResults, int offset) {
        double sinceTimestamp = since.getEpochSecond() + since.getNano() / 1_000_000_000.0;
        
        String queryStr = "SELECT t FROM TableCounter t WHERE t.timestamp >= :since AND t.locationCode = :locationCode ORDER BY t.timestamp ASC, t.frameNumber ASC";
        
        return entityManager.createQuery(queryStr, TableCounter.class)
            .setParameter("since", sinceTimestamp)
            .setParameter("locationCode", locationCode)
            .setMaxResults(maxResults)
            .setFirstResult(offset)
            .getResultList();
    }

    public List<TableCounter> findLatestDataByCameraIdAndLocationCode(String cameraId, String locationCode, Instant since) {
        return findLatestDataByCameraIdAndLocationCodeWithPagination(cameraId, locationCode, since, STREAMING_PAGE_SIZE, 0);
    }
    
    public List<TableCounter> findLatestDataByCameraIdAndLocationCodeWithPagination(String cameraId, String locationCode, Instant since, int maxResults, int offset) {
        double sinceTimestamp = since.getEpochSecond() + since.getNano() / 1_000_000_000.0;
        
        String queryStr = "SELECT t FROM TableCounter t WHERE t.timestamp >= :since AND t.cameraId = :cameraId AND t.locationCode = :locationCode ORDER BY t.timestamp ASC, t.frameNumber ASC";
        
        return entityManager.createQuery(queryStr, TableCounter.class)
            .setParameter("since", sinceTimestamp)
            .setParameter("cameraId", cameraId)
            .setParameter("locationCode", locationCode)
            .setMaxResults(maxResults)
            .setFirstResult(offset)
            .getResultList();
    }
}