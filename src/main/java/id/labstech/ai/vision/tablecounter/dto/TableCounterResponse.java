package id.labstech.ai.vision.tablecounter.dto;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import id.labstech.ai.vision.tablecounter.model.TableCounter;
import java.time.Instant;

public class TableCounterResponse {
    private String type;
    private ObjectNode data;
    private Instant timestamp;
    private String error;
    private String info;

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public TableCounterResponse() {
        this.timestamp = Instant.now();
    }

    public static TableCounterResponse fromTableCounter(TableCounter counter) {
        TableCounterResponse response = new TableCounterResponse();
        response.setType("TABLE_COUNTER_DATA");
        
        try {
            ObjectNode data = objectMapper.createObjectNode();
            data.put("id", counter.getId());
            data.put("cameraId", counter.getCameraId());
            data.put("fetcherId", counter.getFetcherId());
            data.put("frame", counter.getFrame());
            data.put("timestamp", counter.getTimestamp());
            data.put("frameNumber", counter.getFrameNumber());
            data.put("locationId", counter.getLocationId());
            data.put("locationCode", counter.getLocationCode());
            data.put("createdAt", counter.getCreatedAtInstant().toString());

            // Parse and set path as JSON array
            if (counter.getPath() != null) {
                data.set("path", objectMapper.readTree(counter.getPath()));
            }
            
            // Parse and set metadata as JSON object
            if (counter.getMetadata() != null) {
                data.set("metadata", objectMapper.readTree(counter.getMetadata()));
            }
            
            // Parse and set personTracking as JSON object
            if (counter.getPersonTracking() != null) {
                data.set("personTracking", objectMapper.readTree(counter.getPersonTracking()));
            }
            
            // Parse and set turnoverData as JSON object
            if (counter.getTurnoverData() != null) {
                data.set("turnoverData", objectMapper.readTree(counter.getTurnoverData()));
            }

            response.setData(data);
        } catch (Exception e) {
            response.setType("ERROR");
            response.setError("Failed to parse JSON data: " + e.getMessage());
        }
        
        return response;
    }

    public static TableCounterResponse error(String errorMessage) {
        TableCounterResponse response = new TableCounterResponse();
        response.setType("ERROR");
        response.setError(errorMessage);
        return response;
    }

    public static TableCounterResponse info(String infoMessage) {
        TableCounterResponse response = new TableCounterResponse();
        response.setType("INFO");
        response.setInfo(infoMessage);
        return response;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public ObjectNode getData() {
        return data;
    }

    public void setData(ObjectNode data) {
        this.data = data;
    }

    public Instant getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Instant timestamp) {
        this.timestamp = timestamp;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }
}