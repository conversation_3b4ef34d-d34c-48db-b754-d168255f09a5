package id.labstech.ai.vision.tablecounter.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;

public class DateUtil {
    private DateUtil() {
        //do nothing
    }

    public static Date convertFromStringToDate(String dateFrom) throws ParseException {
        SimpleDateFormat genericFormat = new SimpleDateFormat(Constant.GENERIC_FORMAT);
        return genericFormat.parse(dateFrom);
    }

    public static Date convertFromStringToDate(String dateFrom, String format) throws ParseException {
        SimpleDateFormat genericFormat = new SimpleDateFormat(format);
        return genericFormat.parse(dateFrom);
    }

    public static String convertFromDateToString(Date dateFrom, String format) {
        SimpleDateFormat genericFormat = new SimpleDateFormat(format);
        return genericFormat.format(dateFrom);
    }

    public static LocalDateTime convertFromStringToLocalDateTime(String dateFrom) throws ParseException {
        try {
            SimpleDateFormat genericFormat = new SimpleDateFormat(Constant.GENERIC_FORMAT);
            return genericFormat.parse(dateFrom).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        } catch (ParseException e) {
            SimpleDateFormat genericFormat = new SimpleDateFormat(Constant.GENERIC_FORMAT);
            return genericFormat.parse(dateFrom.replace("T", " ").substring(0, 19)).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        }
    }

    public static LocalDateTime convertFromStringToLocalDateTime(String dateFrom, String pattern) throws ParseException {
        try {
            SimpleDateFormat genericFormat = new SimpleDateFormat(pattern);
            return genericFormat.parse(dateFrom).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        } catch (ParseException e) {
            SimpleDateFormat genericFormat = new SimpleDateFormat(pattern);
            return genericFormat.parse(dateFrom.replace("T", " ")).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        }
    }

    public static LocalDate convertFromStringToLocalDate(String dateFrom, String pattern) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return LocalDate.parse(dateFrom, formatter);
    }

    public static LocalDateTime convertFromStringToLocalDateTime(String dateFrom, String pattern, boolean checkNullValue) throws ParseException {
        try {
            if (checkNullValue && dateFrom == null) return null;
            SimpleDateFormat genericFormat = new SimpleDateFormat(pattern);
            return genericFormat.parse(dateFrom).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        } catch (ParseException e) {
            SimpleDateFormat genericFormat = new SimpleDateFormat(pattern);
            return genericFormat.parse(dateFrom.replace("T", " ")).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        }
    }

    public static LocalDateTime convertFromDateToLocalDateTime(Date dateFrom) {
        return dateFrom.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    public static String convertFromLocalDateTimeToString(LocalDateTime dateFrom) {
        SimpleDateFormat sdf = new SimpleDateFormat(Constant.GENERIC_FORMAT);
        return sdf.format(Date.from(dateFrom.atZone(ZoneId.systemDefault()).toInstant()));
    }

    public static String convertFromLocalDateTimeToString(LocalDateTime dateFrom, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(Date.from(dateFrom.atZone(ZoneId.systemDefault()).toInstant()));
    }

    public static String convertFromLocalDateToString(LocalDate dateFrom, String format) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return dateFrom.format(formatter);
    }

    public static String convertLocalDateTimeToOffsetDateTimeToString(LocalDateTime datetime, String format) {
        ZoneOffset offset = ZoneId.systemDefault().getRules().getOffset(datetime);
        OffsetDateTime offDateTime = datetime.atOffset(offset);

        DateTimeFormatter dtf = DateTimeFormatter.ofPattern(format);
        return offDateTime.format(dtf);
    }

    public static LocalDateTime convertOffsetDateTimeInStringToLocalDateTime(String strDateTime, String format) {
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern(format);
        return LocalDateTime.parse(strDateTime, dtf);
    }

    public static Date addSecondsToDate(Date date, long additionalSeconds) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        long t = cal.getTimeInMillis();
        return new Date(t + (additionalSeconds * Constant.ONE_SECOND_IN_MILLIS));
    }

    public static Date addMinutesToDate(Date date, long additionalMinutes) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        long t = cal.getTimeInMillis();
        return new Date(t + (additionalMinutes * Constant.ONE_MINUTE_IN_MILLIS));
    }

    public static Date addYearsToDate(Date date, int additionalYears) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.YEAR, additionalYears);
        return cal.getTime();
    }

    public static LocalDateTime convertFromStringddmmmyyToLocalDateTime(String dateFrom) throws ParseException {
        try {
            SimpleDateFormat ddmmmyy = new SimpleDateFormat(Constant.SHORT_FORMAT);
            return ddmmmyy.parse(dateFrom).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        } catch (ParseException e) {
            SimpleDateFormat ddmmmyy = new SimpleDateFormat(Constant.SHORT_FORMAT);
            return ddmmmyy.parse(dateFrom.replace("T", " ").substring(0, 19)).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        }
    }

    public static LocalDateTime convertFromStringmmmyyToLocalDateTime(String dateFrom) throws ParseException {
        try {
            SimpleDateFormat ddmmmyy = new SimpleDateFormat(Constant.SHORT_FORMAT);
            return ddmmmyy.parse("01-" + dateFrom).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        } catch (ParseException e) {
            SimpleDateFormat ddmmmyy = new SimpleDateFormat(Constant.SHORT_FORMAT);
            return ddmmmyy.parse(dateFrom.replace("T", " ").substring(0, 19)).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        }
    }

    public static String getIndonesianDate(LocalDateTime date) {
        String[] month = new String[]{"", "Januari", "Februari", "Maret", "April", "Mei", "Juni", "Juli", "Agustus", "September", "Oktober", "November", "Desember"};
        return "" + date.getDayOfMonth() + " " + month[date.getMonthValue()] + " " + date.getYear();
    }

    public static String getIndonesianMonth(LocalDateTime date) {
        String[] month = new String[]{"", "Januari", "Februari", "Maret", "April", "Mei", "Juni", "Juli", "Agustus", "September", "Oktober", "November", "Desember"};
        return "" + month[date.getMonthValue()] + " " + date.getYear();
    }

    public static String convertFromLocalDateTimeToStringType2(LocalDateTime dateFrom) {
        SimpleDateFormat sdf = new SimpleDateFormat("dd MMM yyyy HH:mm:ss");
        return sdf.format(Date.from(dateFrom.atZone(ZoneId.systemDefault()).toInstant()));
    }

    public static LocalDateTime convertFromStringYYYYMMDDToLocalDateTime(String dateFrom) throws ParseException {
        SimpleDateFormat formatYYYYMMDD = new SimpleDateFormat("yyyy-MM-dd");
        return formatYYYYMMDD.parse(dateFrom).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    public static LocalDateTime now() {
        return LocalDateTime.now(ZoneId.of(Constant.APPLICATION_GMT));
    }

    public static LocalDateTime dateTimeNowAdjustTimeZone(Integer timezone) {
        return LocalDateTime.now(ZoneId.of(Constant.ZONE_GMT + timezone));
    }

    public static LocalDateTime convertFromStringYYYYMMDDToLocalDateTimeAdjustTimeZone(String dateFrom, Integer timezone) throws ParseException {
        SimpleDateFormat formatYYYYMMDD = new SimpleDateFormat("yyyy-MM-dd");
        return formatYYYYMMDD.parse(dateFrom).toInstant().atZone(ZoneId.of(Constant.ZONE_GMT + timezone)).toLocalDateTime();
    }

    public static String convertFromLocalDateTimeToStringType2AdjustTimeZone(LocalDateTime dateFrom, Integer timezone) {
        SimpleDateFormat sdf = new SimpleDateFormat("dd MMM yyyy HH:mm:ss");
        return sdf.format(Date.from(dateFrom.atZone(ZoneId.of(Constant.ZONE_GMT + timezone)).toInstant()));
    }

    public static LocalDateTime convertFromStringmmmyyToLocalDateTimeAdjustTimeZone(String dateFrom, Integer timezone) throws ParseException {
        try {
            SimpleDateFormat ddmmmyy = new SimpleDateFormat(Constant.SHORT_FORMAT);
            return ddmmmyy.parse("01-" + dateFrom).toInstant().atZone(ZoneId.of(Constant.ZONE_GMT + timezone)).toLocalDateTime();
        } catch (ParseException e) {
            SimpleDateFormat ddmmmyy = new SimpleDateFormat(Constant.SHORT_FORMAT);
            return ddmmmyy.parse(dateFrom.replace("T", " ").substring(0, 19)).toInstant().atZone(ZoneId.of(Constant.ZONE_GMT + timezone)).toLocalDateTime();
        }
    }

    public static LocalDateTime convertFromStringToLocalDateTimeAdjustTimeZone(String dateFrom, Integer timezone) throws ParseException {
        try {
            SimpleDateFormat genericFormat = new SimpleDateFormat(Constant.GENERIC_FORMAT);
            return genericFormat.parse(dateFrom).toInstant().atZone(ZoneId.of(Constant.ZONE_GMT + timezone)).toLocalDateTime();
        } catch (ParseException e) {
            String cleanedDate = dateFrom.replace("T", " ");
            if (cleanedDate.length() > 19) {
                cleanedDate = cleanedDate.substring(0, 19);
            }
            SimpleDateFormat fallbackFormat = new SimpleDateFormat(Constant.GENERIC_FORMAT);
            return fallbackFormat.parse(cleanedDate).toInstant().atZone(ZoneId.of(Constant.ZONE_GMT + timezone)).toLocalDateTime();
        }
    }

    public static LocalDateTime convertFromStringToLocalDateTimeAdjustTimeZone(String dateFrom, String pattern, Integer timezone) throws ParseException {
        try {
            SimpleDateFormat genericFormat = new SimpleDateFormat(pattern);
            return genericFormat.parse(dateFrom).toInstant().atZone(ZoneId.of(Constant.ZONE_GMT + timezone)).toLocalDateTime();
        } catch (ParseException e) {
            SimpleDateFormat genericFormat = new SimpleDateFormat(pattern);
            return genericFormat.parse(dateFrom.replace("T", " ")).toInstant().atZone(ZoneId.of(Constant.ZONE_GMT + timezone)).toLocalDateTime();
        }
    }

    public static LocalDateTime convertFromStringToLocalDateTimeAdjustTimeZone(String dateFrom, String pattern, boolean checkNullValue, Integer timezone) throws ParseException {
        try {
            if (checkNullValue && dateFrom == null) return null;
            SimpleDateFormat genericFormat = new SimpleDateFormat(pattern);
            return genericFormat.parse(dateFrom).toInstant().atZone(ZoneId.of(Constant.ZONE_GMT + timezone)).toLocalDateTime();
        } catch (ParseException e) {
            SimpleDateFormat genericFormat = new SimpleDateFormat(pattern);
            return genericFormat.parse(dateFrom.replace("T", " ")).toInstant().atZone(ZoneId.of(Constant.ZONE_GMT + timezone)).toLocalDateTime();
        }
    }

    public static LocalDateTime convertFromDateToLocalDateTimeAdjustTimeZone(Date dateFrom, Integer timezone) {
        return dateFrom.toInstant().atZone(ZoneId.of(Constant.ZONE_GMT + timezone)).toLocalDateTime();
    }

    public static String convertFromLocalDateTimeToStringAdjustTimeZone(LocalDateTime dateFrom, Integer timezone) {
        SimpleDateFormat sdf = new SimpleDateFormat(Constant.GENERIC_FORMAT);
        return sdf.format(Date.from(dateFrom.atZone(ZoneId.of(Constant.ZONE_GMT + timezone)).toInstant()));
    }

    public static String convertFromLocalDateTimeToStringAdjustTimeZone(LocalDateTime dateFrom, String format, Integer timezone) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(Date.from(dateFrom.atZone(ZoneId.of(Constant.ZONE_GMT + timezone)).toInstant()));
    }

    class Constant {
        static final long ONE_MINUTE_IN_MILLIS = 60000;
        static final long ONE_SECOND_IN_MILLIS = 1000;
        private static final String GENERIC_FORMAT = "yyyy-MM-dd HH:mm:ss";
        private static final String SHORT_FORMAT = "dd-MMM-yy";

        private static final String APPLICATION_GMT = "GMT+7";
        private static final String ZONE_GMT = "GMT+";

        private Constant() {
            // do-nothing
        }
    }
}
